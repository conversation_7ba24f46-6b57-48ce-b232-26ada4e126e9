#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据可视化期末考核任务
作者：AI助手
日期：2024年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

# 设置暖色调配色方案
warm_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE']
sns.set_palette(warm_colors)

class DataVisualizer:
    def __init__(self):
        self.data_path = '数据可视化数据集-A'
        self.output_path = 'visualization_outputs'
        self.create_output_directory()

    def create_output_directory(self):
        """创建输出目录"""
        import os
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path)

    def load_data(self):
        """加载所有数据集"""
        # 加载二手房数据
        self.house_data = pd.read_excel(f'{self.data_path}/二手房数据.xlsx')

        # 加载营销数据
        self.marketing_data = pd.read_excel(f'{self.data_path}/营销和产品销售表.xlsx')

        # 加载GDP数据
        self.gdp_data = pd.read_excel(f'{self.data_path}/国内生产总值季度数据.xlsx')

        print("数据加载完成！")
        print(f"二手房数据: {self.house_data.shape}")
        print(f"营销数据: {self.marketing_data.shape}")
        print(f"GDP数据: {self.gdp_data.shape}")

    # ==================== 二手房数据可视化 ====================

    def plot_house_price_by_district(self):
        """图表1：各区域二手房平均单价条形图"""
        plt.figure(figsize=(12, 8))

        # 计算各区域平均单价
        avg_price = self.house_data.groupby('所在区')['单价（元/平方米）'].mean().sort_values(ascending=True)

        # 创建条形图
        bars = plt.barh(range(len(avg_price)), avg_price.values,
                       color=warm_colors[:len(avg_price)], alpha=0.8, edgecolor='white', linewidth=2)

        # 设置标签和标题
        plt.yticks(range(len(avg_price)), avg_price.index, fontsize=12)
        plt.xlabel('平均单价（元/平方米）', fontsize=14, fontweight='bold')
        plt.title('北京各区域二手房平均单价对比', fontsize=16, fontweight='bold', pad=20)

        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            plt.text(width + 1000, bar.get_y() + bar.get_height()/2,
                    f'{width:,.0f}', ha='left', va='center', fontsize=10, fontweight='bold')

        # 美化图表
        plt.grid(axis='x', alpha=0.3, linestyle='--')
        plt.tight_layout()

        # 保存图表
        plt.savefig(f'{self.output_path}/1_house_price_by_district.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表1：各区域二手房平均单价条形图 - 已保存")

    def plot_house_area_price_scatter(self):
        """图表2：房屋面积与总价散点图"""
        plt.figure(figsize=(12, 8))

        # 创建散点图，按户型着色
        room_types = self.house_data['户型（室）'].unique()
        colors = warm_colors[:len(room_types)]

        for i, room_type in enumerate(sorted(room_types)):
            data = self.house_data[self.house_data['户型（室）'] == room_type]
            plt.scatter(data['面积（平方米）'], data['总价（万元）'],
                       c=colors[i], alpha=0.6, s=60, label=f'{room_type}室', edgecolors='white', linewidth=0.5)

        # 添加趋势线
        z = np.polyfit(self.house_data['面积（平方米）'], self.house_data['总价（万元）'], 1)
        p = np.poly1d(z)
        plt.plot(self.house_data['面积（平方米）'], p(self.house_data['面积（平方米）']),
                "r--", alpha=0.8, linewidth=2, label='趋势线')

        # 设置标签和标题
        plt.xlabel('房屋面积（平方米）', fontsize=14, fontweight='bold')
        plt.ylabel('总价（万元）', fontsize=14, fontweight='bold')
        plt.title('房屋面积与总价关系分析', fontsize=16, fontweight='bold', pad=20)
        plt.legend(fontsize=12, frameon=True, fancybox=True, shadow=True)

        # 美化图表
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.tight_layout()

        # 保存图表
        plt.savefig(f'{self.output_path}/2_house_area_price_scatter.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表2：房屋面积与总价散点图 - 已保存")

    def plot_house_price_boxplot(self):
        """图表3：不同户型房价分布箱形图"""
        plt.figure(figsize=(12, 8))

        # 创建箱形图
        box_plot = plt.boxplot([self.house_data[self.house_data['户型（室）'] == room]['总价（万元）'].values
                               for room in sorted(self.house_data['户型（室）'].unique())],
                              labels=[f'{room}室' for room in sorted(self.house_data['户型（室）'].unique())],
                              patch_artist=True, notch=True)

        # 设置颜色
        for patch, color in zip(box_plot['boxes'], warm_colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        # 设置标签和标题
        plt.xlabel('户型', fontsize=14, fontweight='bold')
        plt.ylabel('总价（万元）', fontsize=14, fontweight='bold')
        plt.title('不同户型二手房价格分布情况', fontsize=16, fontweight='bold', pad=20)

        # 美化图表
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.tight_layout()

        # 保存图表
        plt.savefig(f'{self.output_path}/3_house_price_boxplot.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表3：不同户型房价分布箱形图 - 已保存")

    # ==================== 营销数据可视化 ====================

    def plot_marketing_trend_line(self):
        """图表4：营销费用与订单金额时间趋势折线图"""
        plt.figure(figsize=(14, 8))

        # 创建双y轴图
        fig, ax1 = plt.subplots(figsize=(14, 8))

        # 绘制营销费用
        color1 = warm_colors[0]
        ax1.set_xlabel('日期', fontsize=14, fontweight='bold')
        ax1.set_ylabel('营销费用（元）', color=color1, fontsize=14, fontweight='bold')
        line1 = ax1.plot(self.marketing_data['日期'], self.marketing_data['营销费用（元）'],
                        color=color1, linewidth=3, marker='o', markersize=6, label='营销费用')
        ax1.tick_params(axis='y', labelcolor=color1)
        ax1.grid(True, alpha=0.3, linestyle='--')

        # 创建第二个y轴
        ax2 = ax1.twinx()
        color2 = warm_colors[1]
        ax2.set_ylabel('订单金额（元）', color=color2, fontsize=14, fontweight='bold')
        line2 = ax2.plot(self.marketing_data['日期'], self.marketing_data['订单金额（元）'],
                        color=color2, linewidth=3, marker='s', markersize=6, label='订单金额')
        ax2.tick_params(axis='y', labelcolor=color2)

        # 设置标题
        plt.title('营销费用与订单金额时间趋势分析', fontsize=16, fontweight='bold', pad=20)

        # 添加图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper left', fontsize=12, frameon=True, fancybox=True, shadow=True)

        # 旋转x轴标签
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        plt.savefig(f'{self.output_path}/4_marketing_trend_line.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表4：营销费用与订单金额时间趋势折线图 - 已保存")

    def plot_marketing_bubble_chart(self):
        """图表5：展现量、点击量与订单金额气泡图"""
        plt.figure(figsize=(12, 8))

        # 创建气泡图
        scatter = plt.scatter(self.marketing_data['展现量'], self.marketing_data['点击量'],
                            s=self.marketing_data['订单金额（元）']/20, # 气泡大小
                            c=self.marketing_data['营销费用（元）'], # 颜色映射
                            cmap='YlOrRd', alpha=0.7, edgecolors='white', linewidth=2)

        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('营销费用（元）', fontsize=12, fontweight='bold')

        # 设置标签和标题
        plt.xlabel('展现量', fontsize=14, fontweight='bold')
        plt.ylabel('点击量', fontsize=14, fontweight='bold')
        plt.title('展现量、点击量与订单金额关系分析\n（气泡大小代表订单金额，颜色代表营销费用）',
                 fontsize=16, fontweight='bold', pad=20)

        # 美化图表
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.tight_layout()

        # 保存图表
        plt.savefig(f'{self.output_path}/5_marketing_bubble_chart.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表5：展现量、点击量与订单金额气泡图 - 已保存")

    def plot_marketing_radar_chart(self):
        """图表6：营销效果综合评估雷达图"""
        # 计算营销效果指标
        metrics = {
            '点击率': (self.marketing_data['点击量'].sum() / self.marketing_data['展现量'].sum()) * 100,
            '转化率': (self.marketing_data['下单新客数'].sum() / self.marketing_data['点击量'].sum()) * 100,
            'ROI': (self.marketing_data['订单金额（元）'].sum() / self.marketing_data['营销费用（元）'].sum()) * 100,
            '加购率': (self.marketing_data['加购数'].sum() / self.marketing_data['点击量'].sum()) * 100,
            '关注度': (self.marketing_data['商品关注数'].sum() / self.marketing_data['进店数'].sum()) * 100
        }

        # 标准化数据到0-100范围
        max_values = {'点击率': 5, '转化率': 20, 'ROI': 500, '加购率': 50, '关注度': 50}
        normalized_values = [min(100, (metrics[key] / max_values[key]) * 100) for key in metrics.keys()]

        # 创建雷达图
        fig = go.Figure()

        fig.add_trace(go.Scatterpolar(
            r=normalized_values,
            theta=list(metrics.keys()),
            fill='toself',
            name='营销效果',
            line_color='rgb(255, 107, 107)',
            fillcolor='rgba(255, 107, 107, 0.3)'
        ))

        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title={
                'text': '营销效果综合评估雷达图',
                'x': 0.5,
                'font': {'size': 16, 'family': 'SimHei'}
            },
            font=dict(family="SimHei", size=12)
        )

        # 保存图表
        fig.write_image(f'{self.output_path}/6_marketing_radar_chart.png', width=800, height=600)
        fig.show()
        print("图表6：营销效果综合评估雷达图 - 已保存")

    # ==================== GDP数据可视化 ====================

    def plot_gdp_area_chart(self):
        """图表7：GDP三大产业结构变化趋势面积图"""
        # 数据预处理
        gdp_melted = self.gdp_data.set_index('指标').T
        gdp_melted.index = pd.to_datetime(gdp_melted.index.str.replace('年第', '-Q').str.replace('季度', ''),
                                         format='%Y-Q%q')
        gdp_melted = gdp_melted.sort_index()

        # 创建面积图
        plt.figure(figsize=(14, 8))

        # 绘制堆叠面积图
        plt.stackplot(gdp_melted.index,
                     gdp_melted['第一产业增加值（亿元）'],
                     gdp_melted['第二产业增加值（亿元）'],
                     gdp_melted['第三产业增加值（亿元）'],
                     labels=['第一产业', '第二产业', '第三产业'],
                     colors=warm_colors[:3], alpha=0.8)

        # 设置标签和标题
        plt.xlabel('时间', fontsize=14, fontweight='bold')
        plt.ylabel('增加值（亿元）', fontsize=14, fontweight='bold')
        plt.title('中国GDP三大产业结构变化趋势（2019-2022）', fontsize=16, fontweight='bold', pad=20)
        plt.legend(loc='upper left', fontsize=12, frameon=True, fancybox=True, shadow=True)

        # 美化图表
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        plt.savefig(f'{self.output_path}/7_gdp_area_chart.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表7：GDP三大产业结构变化趋势面积图 - 已保存")

    def plot_gdp_donut_chart(self):
        """图表8：最新季度三大产业占比环形图"""
        # 获取最新季度数据（2022年第四季度）
        latest_data = self.gdp_data.set_index('指标')['2022年第四季度']
        industry_data = latest_data[1:4]  # 三大产业数据

        # 创建环形图
        fig, ax = plt.subplots(figsize=(10, 10))

        # 绘制饼图
        wedges, texts, autotexts = ax.pie(industry_data.values,
                                         labels=['第一产业', '第二产业', '第三产业'],
                                         colors=warm_colors[:3],
                                         autopct='%1.1f%%',
                                         startangle=90,
                                         pctdistance=0.85,
                                         wedgeprops=dict(width=0.5, edgecolor='white', linewidth=2))

        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(12)
            autotext.set_fontweight('bold')

        for text in texts:
            text.set_fontsize(14)
            text.set_fontweight('bold')

        # 添加中心文本
        plt.text(0, 0, '2022年Q4\nGDP结构', ha='center', va='center',
                fontsize=16, fontweight='bold')

        # 设置标题
        plt.title('2022年第四季度三大产业占比分析', fontsize=16, fontweight='bold', pad=20)

        # 保存图表
        plt.savefig(f'{self.output_path}/8_gdp_donut_chart.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表8：最新季度三大产业占比环形图 - 已保存")

    def plot_gdp_heatmap(self):
        """图表9：各产业季度增长率热力图"""
        # 数据预处理
        gdp_data_clean = self.gdp_data.set_index('指标').T
        gdp_data_clean.index = gdp_data_clean.index.str.replace('年第', '-Q').str.replace('季度', '')

        # 计算同比增长率
        growth_rates = []
        quarters = gdp_data_clean.index.tolist()

        for i in range(4, len(quarters)):  # 从第5个季度开始计算同比增长率
            current_quarter = gdp_data_clean.iloc[i, 1:4]  # 三大产业数据
            previous_year_quarter = gdp_data_clean.iloc[i-4, 1:4]  # 去年同期数据
            growth_rate = ((current_quarter - previous_year_quarter) / previous_year_quarter * 100)
            growth_rates.append(growth_rate.values)

        # 创建热力图数据
        growth_df = pd.DataFrame(growth_rates,
                               index=quarters[4:],
                               columns=['第一产业', '第二产业', '第三产业'])

        # 创建热力图
        plt.figure(figsize=(12, 8))

        # 绘制热力图
        sns.heatmap(growth_df.T, annot=True, fmt='.1f', cmap='RdYlBu_r',
                   center=0, cbar_kws={'label': '同比增长率 (%)'},
                   linewidths=0.5, linecolor='white')

        # 设置标签和标题
        plt.xlabel('季度', fontsize=14, fontweight='bold')
        plt.ylabel('产业类型', fontsize=14, fontweight='bold')
        plt.title('各产业季度同比增长率热力图（2020-2022）', fontsize=16, fontweight='bold', pad=20)

        # 旋转x轴标签
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        plt.savefig(f'{self.output_path}/9_gdp_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表9：各产业季度增长率热力图 - 已保存")

if __name__ == "__main__":
    # 创建可视化对象
    visualizer = DataVisualizer()

    # 加载数据
    visualizer.load_data()

    # 生成二手房数据可视化
    print("\n开始生成二手房数据可视化...")
    visualizer.plot_house_price_by_district()
    visualizer.plot_house_area_price_scatter()
    visualizer.plot_house_price_boxplot()

    # 生成营销数据可视化
    print("\n开始生成营销数据可视化...")
    visualizer.plot_marketing_trend_line()
    visualizer.plot_marketing_bubble_chart()
    visualizer.plot_marketing_radar_chart()

    # 生成GDP数据可视化
    print("\n开始生成GDP数据可视化...")
    visualizer.plot_gdp_area_chart()
    visualizer.plot_gdp_donut_chart()
    visualizer.plot_gdp_heatmap()

    print("\n所有可视化图表生成完成！")
